from pxr import Usd, UsdGeom, Gf
import omni.usd

def get_frustum_planes(camera_prim, time=Usd.TimeCode.Default()):
    cam = UsdGeom.Camera(camera_prim)
    try:
        gfcam = cam.GetCamera(time)
        view = gfcam.frustum.ComputeViewMatrix()
        proj = gfcam.frustum.ComputeProjectionMatrix()
        vp = view * proj
        print("Camera found:", camera_prim.GetPath())
    except Exception as e:
        print("ERROR: camera matrix failed:", e)
        return None

    idxs = [(3,0,0,0,+1),(3,0,0,0,-1),
            (3,1,1,1,+1),(3,1,1,1,-1),
            (3,2,2,2,+1),(3,2,2,2,-1)]
    planes = []
    for (a,b,c,d,sign) in idxs:
        planes.append(Gf.Vec4f(
            vp[a][3] + sign*vp[d][3],
            vp[a][0] + sign*vp[d][0],
            vp[a][1] + sign*vp[d][1],
            vp[a][2] + sign*vp[d][2]))
    print("Extracted frustum planes.")
    return planes

def aabb_in_frustum(minPt, maxPt, planes):
    for i, p in enumerate(planes):
        n = Gf.Vec3f(p[1], p[2], p[3])
        d = p[0]
        vp = Gf.Vec3f(
            maxPt[0] if n[0] >= 0 else minPt[0],
            maxPt[1] if n[1] >= 0 else minPt[1],
            maxPt[2] if n[2] >= 0 else minPt[2],
        )
        if Gf.Dot(n, vp) + d < 0:
            return False, i
    return True, None

def cull_parent_of_nurec(stage, camera_path="/World/Camera"):
    print(">> Starting parent-of-parent culling")
    camera = stage.GetPrimAtPath(camera_path)
    if not camera:
        print("ERROR: Camera prim not found:", camera_path)
        return
    planes = get_frustum_planes(camera)
    if not planes:
        print("ERROR: Failed to compute frustum planes")
        return

    total = 0
    hit = 0
    culled = set()

    for prim in stage.Traverse():
        total += 1
        attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
        attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")
        if not attr_min or not attr_max:
            continue
        hit += 1
        print(f"Found crop-bounds prim: {prim.GetPath()}")
        try:
            minB = Gf.Vec3f(*attr_min.Get())
            maxB = Gf.Vec3f(*attr_max.Get())
            print(f"  Bounds: min={minB}, max={maxB}")
        except Exception as e:
            print("  ERR reading bounds:", e)
            continue

        visible, plane_idx = aabb_in_frustum(minB, maxB, planes)
        parent2 = prim.GetParent().GetParent()
        if not parent2 or not parent2.IsValid():
            print("  WARNING: parent.parent invalid for", prim.GetPath())
            continue
        vis_attr = UsdGeom.Imageable(parent2).GetVisibilityAttr()
        vis_attr.Set(UsdGeom.Tokens.inherited if visible else UsdGeom.Tokens.invisible)
        print(f"  Parent parent: {parent2.GetPath()} visibility set to {'inherited' if visible else 'invisible'}; visible={visible}" +
              (f", culled by plane {plane_idx}" if plane_idx is not None else ""))

        if not visible:
            culled.add(parent2.GetPath())

    print(f"Total prims scanned: {total}, with crop-bounds: {hit}, unique parents culled: {len(culled)}")

if __name__ == "__main__":
    stage = omni.usd.get_context().get_stage()
    cull_parent_of_nurec(stage, camera_path="/World/Camera")
