from pxr import Usd, UsdGeom, Sdf, Gf
import omni.usd
import os
import re
import time

def safe_name(name):
    """清理 prim 名称，防止非法字符导致导入失败"""
    name = os.path.splitext(name)[0]
    return re.sub(r'[^a-zA-Z0-9_]', '_', name)

def compute_x_bound(stage, prim_path):
    """
    获取 prim 的包围盒宽度（x方向）。
    使用 ComputeWorldBound → 转为本地单位计算 x 长度。
    """
    try:
        prim = stage.GetPrimAtPath(prim_path)
        boundable = UsdGeom.Boundable(prim)
        if not boundable:
            return 2.0  # fallback 宽度
        bounds = boundable.ComputeExtent(Usd.TimeCode.Default())
        if not bounds or len(bounds) < 2:
            return 2.0
        min_x = bounds[0][0]
        max_x = bounds[1][0]
        return max_x - min_x
    except Exception as e:
        print(f"⚠️ 无法计算 {prim_path} 的宽度: {e}")
        return 2.0

# 配置
usdz_folder = "D:/wanleqi/FlorenzVillage/usdz_output/16"  # <<< 替换成你自己的路径
usdz_files = [f for f in os.listdir(usdz_folder) if f.endswith(".usdz")]
stage = omni.usd.get_context().get_stage()
base_prim_path = "/Imported"
spacing = 0.0  # 间隙

# 确保 /Imported prim 存在
UsdGeom.Xform.Define(stage, Sdf.Path(base_prim_path))

# 排布逻辑
current_x = 0.0

for i, filename in enumerate(usdz_files):
    clean_name = safe_name(filename)
    prim_path = f"{base_prim_path}/{clean_name}"
    filepath = os.path.join(usdz_folder, filename).replace("\\", "/")

    print(f"📦 导入: {filepath} 到 {prim_path}")

    # 创建 Xform 并添加 Reference
    xform = UsdGeom.Xform.Define(stage, Sdf.Path(prim_path))
    xform.GetPrim().GetReferences().AddReference(filepath)

    # 等待参考解析（有时异步加载）
    omni.usd.get_context().get_stage().Flatten()
    time.sleep(0.1)  # 稍微等一下加载

    # 设置 X 方向位置
    xform.AddTranslateOp().Set((current_x, 0, 0))

    # 获取宽度
    width = compute_x_bound(stage, prim_path)
    current_x += width + spacing
