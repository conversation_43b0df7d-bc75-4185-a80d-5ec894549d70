from pxr import Usd, UsdGeom, Gf, Sdf, UsdShade
import omni.usd

def make_wireframe_box(stage, parent, minB, maxB, name="BBoxWire"):
    """
    在 parent 下创建一个可见的 wireframe 类型的 box，表示从 minB 到 maxB 范围。
    使用简单的方法创建12条独立的线段。
    """
    # 创建一个 Xform 作为容器
    container_path = parent.GetPath().AppendChild(name)
    container = UsdGeom.Xform.Define(stage, container_path)

    # 8 corners of the bounding box
    corners = [
        Gf.Vec3f(minB[0], minB[1], minB[2]),  # 0: min corner
        Gf.Vec3f(maxB[0], minB[1], minB[2]),  # 1: +X
        Gf.Vec3f(maxB[0], maxB[1], minB[2]),  # 2: +X+Y
        Gf.Vec3f(minB[0], maxB[1], minB[2]),  # 3: +Y
        Gf.Vec3f(minB[0], minB[1], maxB[2]),  # 4: +Z
        Gf.Vec3f(maxB[0], minB[1], maxB[2]),  # 5: +X+Z
        Gf.Vec3f(maxB[0], maxB[1], maxB[2]),  # 6: +X+Y+Z (max corner)
        Gf.Vec3f(minB[0], maxB[1], maxB[2]),  # 7: +Y+Z
    ]

    # Define the 12 edges of the box
    edges = [
        # Bottom face (Z = minB[2])
        (0, 1), (1, 2), (2, 3), (3, 0),
        # Top face (Z = maxB[2])
        (4, 5), (5, 6), (6, 7), (7, 4),
        # Vertical edges
        (0, 4), (1, 5), (2, 6), (3, 7)
    ]

    # Create each edge as a separate BasisCurves
    for i, (start_idx, end_idx) in enumerate(edges):
        edge_path = container_path.AppendChild(f"Edge_{i}")
        edge_curve = UsdGeom.BasisCurves.Define(stage, edge_path)

        # Set the two points for this edge
        edge_points = [corners[start_idx], corners[end_idx]]
        edge_curve.CreatePointsAttr(edge_points)
        edge_curve.CreateCurveVertexCountsAttr([2])  # One line with 2 points
        edge_curve.CreateTypeAttr(UsdGeom.Tokens.linear)

        # Set display properties
        edge_curve.CreateDisplayColorAttr([Gf.Vec3f(1.0, 0.0, 0.0)])  # Red color
        edge_curve.CreateDisplayOpacityAttr([1.0])
        edge_curve.CreateWidthsAttr([3.0, 3.0])  # Width for both points

    return container

def create_test_nurec_prim(stage, prim_path="/World/TestNurec", minBounds=(-1, -1, -1), maxBounds=(1, 1, 1)):
    """
    创建一个测试用的 NUREC prim，包含 omni:nurec:crop:minBounds 和 maxBounds 属性
    """
    # 创建一个 Xform prim 作为测试对象
    xform = UsdGeom.Xform.Define(stage, prim_path)
    prim = xform.GetPrim()

    # 添加 NUREC crop bounds 属性
    min_attr = prim.CreateAttribute("omni:nurec:crop:minBounds", Sdf.ValueTypeNames.Float3)
    max_attr = prim.CreateAttribute("omni:nurec:crop:maxBounds", Sdf.ValueTypeNames.Float3)

    min_attr.Set(Gf.Vec3f(*minBounds))
    max_attr.Set(Gf.Vec3f(*maxBounds))

    print(f"Created test NUREC prim at {prim_path} with bounds {minBounds} to {maxBounds}")
    return prim

def visualize_nurec_wire(stage):
    print("Visualizing wireframe bounds for NUREC prims")
    count = 0
    skipped_count = 0

    for prim in stage.Traverse():

        attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
        attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")
        if not attr_min or not attr_max:
            continue

        # Check visibility - skip if prim or its ancestors are invisible
        imageable = UsdGeom.Imageable(prim)
        if imageable:
            visibility = imageable.ComputeVisibility()
            if visibility == UsdGeom.Tokens.invisible:
                skipped_count += 1
                print(f"[SKIP] {prim.GetPath()} - invisible (visibility={visibility})")
                continue

        # Also check parent's parent visibility (the one that gets culled)
        parent2 = prim.GetParent().GetParent() if prim.GetParent() else None
        if parent2 and parent2.IsValid():
            parent2_imageable = UsdGeom.Imageable(parent2)
            if parent2_imageable:
                parent2_visibility = parent2_imageable.ComputeVisibility()
                if parent2_visibility == UsdGeom.Tokens.invisible:
                    skipped_count += 1
                    print(f"[SKIP] {prim.GetPath()} - parent.parent invisible ({parent2.GetPath()}, visibility={parent2_visibility})")
                    continue

        count += 1
        minB = Gf.Vec3f(*attr_min.Get())
        maxB = Gf.Vec3f(*attr_max.Get())
        print(f"[{count}] {prim.GetPath()} bounds {minB} – {maxB}")

        # 在同一个父级下创建线框，而不是父级的父级
        parent = prim.GetParent()
        if not parent or not parent.IsValid():
            parent = stage.GetDefaultPrim()
        make_wireframe_box(stage, parent, minB, maxB, name=f"WireBBox_{count}")

    print(f"Wireframe boxes created for {count} prim(s), skipped {skipped_count} invisible prim(s).")

def test_first_nurec_bounds(stage):
    """
    测试第一个 NUREC prim 的 bounding box 可视化
    """
    print("=== Testing first NUREC prim bounds visualization ===")

    # 首先创建一个测试 NUREC prim
    test_prim = create_test_nurec_prim(stage, "/World/TestNurec1", (-2, -1, -0.5), (2, 1, 0.5))

    # 可视化所有 NUREC prims 的边界框
    visualize_nurec_wire(stage)

    print("=== Test completed ===")

if __name__ == "__main__":
    stage = omni.usd.get_context().get_stage()
    test_first_nurec_bounds(stage)
