from pxr import Usd, UsdGeom, Gf, Sdf
import omni.usd

def make_wireframe_box(stage, parent, minB, maxB, name="BBoxWire"):
    """
    在 parent 下创建一个 wireframe 类型的 box mesh，表示从 minB 到 maxB 范围。
    """
    prim_path = parent.GetPath().AppendChild(name)
    mesh = UsdGeom.Mesh.Define(stage, prim_path)
    mesh.GetDisplayColorAttr().Set([Gf.Vec3f(1.0,0.0,0.0)])
    mesh.GetPurposeAttr().Set("proxy")

    # 8 corners
    corners = [
        Gf.Vec3f(minB[0], minB[1], minB[2]),
        Gf.Vec3f(maxB[0], minB[1], minB[2]),
        Gf.Vec3f(maxB[0], maxB[1], minB[2]),
        Gf.Vec3f(minB[0], maxB[1], minB[2]),
        Gf.Vec3f(minB[0], minB[1], maxB[2]),
        Gf.Vec3f(maxB[0], minB[1], maxB[2]),
        Gf.Vec3f(maxB[0], maxB[1], maxB[2]),
        Gf.Vec3f(minB[0], maxB[1], maxB[2]),
    ]
    mesh.CreatePointsAttr(corners)

    # define 12 edges as line segments
    # each edge is two indices
    edges = [
        (0,1),(1,2),(2,3),(3,0),
        (4,5),(5,6),(6,7),(7,4),
        (0,4),(1,5),(2,6),(3,7),
    ]
    # make as line segments: each segment is 2 verts, use separate primvars
    mesh.CreateFaceVertexCountsAttr([2]*len(edges))
    mesh.CreateFaceVertexIndicesAttr([i for e in edges for i in e])

    # disable shading by giving invisible extent
    mesh.CreateExtentAttr([(minB[0],minB[1],minB[2]),(maxB[0],maxB[1],maxB[2])])
    return mesh

def visualize_nurec_wire(stage):
    print("Visualizing wireframe bounds for NUREC prims")
    count = 0
    for prim in stage.Traverse():
        attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
        attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")
        if not attr_min or not attr_max:
            continue
        count += 1
        minB = Gf.Vec3f(*attr_min.Get())
        maxB = Gf.Vec3f(*attr_max.Get())
        print(f"[{count}] {prim.GetPath()} bounds {minB} – {maxB}")

        parent2 = prim.GetParent().GetParent()
        if not parent2 or not parent2.IsValid():
            parent2 = prim.GetParent()
        make_wireframe_box(stage, parent2, minB, maxB, name=f"WireBBox_{count}")
    print(f"Wireframe boxes created for {count} prim(s).")

if __name__ == "__main__":
    stage = omni.usd.get_context().get_stage()
    visualize_nurec_wire(stage)
