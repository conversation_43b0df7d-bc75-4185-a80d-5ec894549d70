from pxr import Usd, UsdGeom, Gf, Sdf, UsdShade
import omni.usd

def make_wireframe_box(stage, parent, minB, maxB, name="BBoxWire"):
    """
    在 parent 下创建一个可见的 wireframe 类型的 box，表示从 minB 到 maxB 范围。
    使用 BasisCurves 来创建线框，这样更适合显示边框。
    """
    prim_path = parent.GetPath().AppendChild(name)

    # 使用 BasisCurves 而不是 Mesh 来创建线框
    curves = UsdGeom.BasisCurves.Define(stage, prim_path)

    # 8 corners of the bounding box
    corners = [
        Gf.Vec3f(minB[0], minB[1], minB[2]),  # 0: min corner
        Gf.Vec3f(maxB[0], minB[1], minB[2]),  # 1: +X
        Gf.Vec3f(maxB[0], maxB[1], minB[2]),  # 2: +X+Y
        Gf.Vec3f(minB[0], maxB[1], minB[2]),  # 3: +Y
        Gf.Vec3f(minB[0], minB[1], maxB[2]),  # 4: +Z
        Gf.Vec3f(maxB[0], minB[1], maxB[2]),  # 5: +X+Z
        Gf.Vec3f(maxB[0], maxB[1], maxB[2]),  # 6: +X+Y+Z (max corner)
        Gf.Vec3f(minB[0], maxB[1], maxB[2]),  # 7: +Y+Z
    ]

    # Define the 12 edges of the box as separate line segments
    # Bottom face (Z = minB[2])
    bottom_edges = [0, 1, 1, 2, 2, 3, 3, 0]
    # Top face (Z = maxB[2])
    top_edges = [4, 5, 5, 6, 6, 7, 7, 4]
    # Vertical edges
    vertical_edges = [0, 4, 1, 5, 2, 6, 3, 7]

    # Combine all edge points
    all_points = []
    edge_indices = bottom_edges + top_edges + vertical_edges
    for idx in edge_indices:
        all_points.append(corners[idx])

    # Set curve properties
    curves.CreatePointsAttr(all_points)
    curves.CreateCurveVertexCountsAttr([2] * 12)  # 12 line segments, each with 2 points
    curves.CreateTypeAttr(UsdGeom.Tokens.linear)  # Linear curves (straight lines)
    curves.CreateBasisAttr(UsdGeom.Tokens.bezier)  # Bezier basis

    # Set display color to bright red for visibility
    curves.CreateDisplayColorAttr([Gf.Vec3f(1.0, 0.0, 0.0)])
    curves.CreateDisplayOpacityAttr([1.0])

    # Set width for better visibility
    curves.CreateWidthsAttr([2.0] * len(all_points))

    # Set purpose to default (not proxy) for visibility
    curves.GetPurposeAttr().Set(UsdGeom.Tokens.default)

    # Set extent for proper culling
    curves.CreateExtentAttr([minB, maxB])

    return curves

def create_test_nurec_prim(stage, prim_path="/World/TestNurec", minBounds=(-1, -1, -1), maxBounds=(1, 1, 1)):
    """
    创建一个测试用的 NUREC prim，包含 omni:nurec:crop:minBounds 和 maxBounds 属性
    """
    # 创建一个 Xform prim 作为测试对象
    xform = UsdGeom.Xform.Define(stage, prim_path)
    prim = xform.GetPrim()

    # 添加 NUREC crop bounds 属性
    min_attr = prim.CreateAttribute("omni:nurec:crop:minBounds", Sdf.ValueTypeNames.Float3)
    max_attr = prim.CreateAttribute("omni:nurec:crop:maxBounds", Sdf.ValueTypeNames.Float3)

    min_attr.Set(Gf.Vec3f(*minBounds))
    max_attr.Set(Gf.Vec3f(*maxBounds))

    print(f"Created test NUREC prim at {prim_path} with bounds {minBounds} to {maxBounds}")
    return prim

def visualize_nurec_wire(stage):
    print("Visualizing wireframe bounds for NUREC prims")
    count = 0
    for prim in stage.Traverse():
        attr_min = prim.GetAttribute("omni:nurec:crop:minBounds")
        attr_max = prim.GetAttribute("omni:nurec:crop:maxBounds")
        if not attr_min or not attr_max:
            continue
        count += 1
        minB = Gf.Vec3f(*attr_min.Get())
        maxB = Gf.Vec3f(*attr_max.Get())
        print(f"[{count}] {prim.GetPath()} bounds {minB} – {maxB}")

        # 在同一个父级下创建线框，而不是父级的父级
        parent = prim.GetParent()
        if not parent or not parent.IsValid():
            parent = stage.GetDefaultPrim()
        make_wireframe_box(stage, parent, minB, maxB, name=f"WireBBox_{count}")
    print(f"Wireframe boxes created for {count} prim(s).")

def test_first_nurec_bounds(stage):
    """
    测试第一个 NUREC prim 的 bounding box 可视化
    """
    print("=== Testing first NUREC prim bounds visualization ===")

    # 首先创建一个测试 NUREC prim
    test_prim = create_test_nurec_prim(stage, "/World/TestNurec1", (-2, -1, -0.5), (2, 1, 0.5))

    # 可视化所有 NUREC prims 的边界框
    visualize_nurec_wire(stage)

    print("=== Test completed ===")

if __name__ == "__main__":
    stage = omni.usd.get_context().get_stage()
    test_first_nurec_bounds(stage)
